"""
Unified calculation module for Rise of Kingdoms statistics.
All calculation logic should be centralized here to ensure consistency.
"""

from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class PlayerStats:
    """Standardized player statistics structure."""
    player_id: int
    governor_id: str
    name: str
    alliance: Optional[str]
    power: int
    total_kill_points: int
    dead_troops: int
    kill_points_t1: int = 0
    kill_points_t2: int = 0
    kill_points_t3: int = 0
    kill_points_t4: int = 0
    kill_points_t5: int = 0
    ranged_points: int = 0
    total_kills: int = 0

@dataclass
class DeltaStats:
    """Standardized delta statistics structure."""
    player_id: int
    governor_id: str
    name: str
    alliance: Optional[str]
    power_delta: int
    kill_points_delta: int
    dead_troops_delta: int
    t4_delta: int = 0
    t5_delta: int = 0
    t45_delta: int = 0
    kp_per_dead: float = 0.0
    efficiency_score: float = 0.0
    is_zeroed: bool = False
    is_new_player: bool = False
    is_top_performer: bool = False
    needs_improvement: bool = False

class RoKCalculator:
    """Centralized calculator for all Rise of Kingdoms statistics."""

    # Configuration constants
    ZEROED_POWER_THRESHOLD = 0.8  # 80% power loss
    TOP_PERFORMER_KP_THRESHOLD = 100_000_000  # 100M KP
    UNDERPERFORMER_KP_THRESHOLD = 10_000_000  # 10M KP
    MIN_DEAD_FOR_EFFICIENCY = 1000  # Minimum dead troops for efficiency calculation

    @staticmethod
    def calculate_delta_stats(current: PlayerStats, baseline: PlayerStats) -> DeltaStats:
        """Calculate delta statistics between two player stat snapshots."""

        # Null-safe value extraction
        current_power = current.power or 0
        baseline_power = baseline.power or 0
        current_kp = current.total_kill_points or 0
        baseline_kp = baseline.total_kill_points or 0
        current_dead = current.dead_troops or 0
        baseline_dead = baseline.dead_troops or 0
        current_t4 = current.kill_points_t4 or 0
        baseline_t4 = baseline.kill_points_t4 or 0
        current_t5 = current.kill_points_t5 or 0
        baseline_t5 = baseline.kill_points_t5 or 0

        # Basic deltas with null safety
        power_delta = current_power - baseline_power
        kp_delta = current_kp - baseline_kp
        dead_delta = current_dead - baseline_dead

        # Handle negative KP deltas (data quality issue)
        if kp_delta < 0:
            logger.warning(f"Negative KP delta for player {current.governor_id}: {kp_delta} (current: {current_kp}, baseline: {baseline_kp})")
            # For RoK, KP should never decrease - this indicates data issues
            # Keep the negative value for investigation but flag it

        # T4/T5 deltas with null safety
        t4_delta = current_t4 - baseline_t4
        t5_delta = current_t5 - baseline_t5
        t45_delta = t4_delta + t5_delta

        # Calculate efficiency metrics
        kp_per_dead = RoKCalculator.calculate_kp_per_dead(kp_delta, dead_delta)
        efficiency_score = RoKCalculator.calculate_efficiency_score(kp_delta, power_delta, dead_delta)

        # Determine player status with improved logic
        is_zeroed = RoKCalculator.is_player_zeroed(power_delta, baseline_power, dead_delta)
        is_new_player = baseline_kp == 0 and baseline_power == 0  # New player if no baseline stats
        is_top_performer = kp_delta >= RoKCalculator.TOP_PERFORMER_KP_THRESHOLD and kp_delta > 0
        needs_improvement = (
            not is_new_player and
            kp_delta == 0 and  # Zero KP change indicates no activity
            not is_zeroed  # Don't flag zeroed players as underperforming
        )

        return DeltaStats(
            player_id=current.player_id,
            governor_id=current.governor_id,
            name=current.name,
            alliance=current.alliance,
            power_delta=power_delta,
            kill_points_delta=kp_delta,
            dead_troops_delta=dead_delta,
            t4_delta=t4_delta,
            t5_delta=t5_delta,
            t45_delta=t45_delta,
            kp_per_dead=kp_per_dead,
            efficiency_score=efficiency_score,
            is_zeroed=is_zeroed,
            is_new_player=is_new_player,
            is_top_performer=is_top_performer,
            needs_improvement=needs_improvement
        )

    @staticmethod
    def calculate_kp_per_dead(kp_delta: int, dead_delta: int) -> float:
        """Calculate kill points per dead troop ratio with improved logic."""
        # Only calculate ratio if both values are positive
        if dead_delta <= 0 or kp_delta <= 0:
            return 0.0

        # Sanity check for unrealistic ratios (likely data errors)
        ratio = kp_delta / dead_delta
        if ratio > 1000:  # More than 1000 KP per dead troop is unrealistic
            logger.warning(f"Unrealistic KP/Dead ratio: {ratio} (KP: {kp_delta}, Dead: {dead_delta})")

        return round(ratio, 2)

    @staticmethod
    def calculate_efficiency_score(kp_delta: int, power_delta: int, dead_delta: int) -> float:
        """Calculate player efficiency score based on KP gained vs resources lost."""
        if kp_delta <= 0:
            return 0.0

        # Scenario 1: Player lost power but gained KP (efficient combat)
        if power_delta < 0:
            efficiency = kp_delta / abs(power_delta)
            # Cap efficiency at reasonable maximum (1000 KP per power lost)
            return round(min(efficiency, 1000.0), 4)

        # Scenario 2: Player gained both power and KP
        if power_delta > 0:
            # Factor in dead troops if significant
            if dead_delta > RoKCalculator.MIN_DEAD_FOR_EFFICIENCY:
                # Weight dead troops less heavily (100:1 ratio)
                efficiency = kp_delta / (power_delta + dead_delta * 0.01)
            else:
                # Simple KP to power ratio
                efficiency = kp_delta / power_delta
            return round(min(efficiency, 1000.0), 4)

        # Scenario 3: No power change but gained KP (very efficient)
        if power_delta == 0:
            # Normalize to a reasonable scale
            return round(min(kp_delta / 1_000_000, 100.0), 4)

        return 0.0

    @staticmethod
    def is_player_zeroed(power_delta: int, baseline_power: int, dead_delta: int) -> bool:
        """Determine if a player has been zeroed with improved logic."""
        if baseline_power <= 0:
            return False

        # Only consider power losses
        if power_delta >= 0:
            return False

        power_loss_percentage = abs(power_delta) / baseline_power

        # Player is considered zeroed if they lost 80%+ of their power
        # OR they lost significant power (50%+) with very high dead troops
        high_power_loss = power_loss_percentage >= RoKCalculator.ZEROED_POWER_THRESHOLD
        moderate_loss_high_deads = (
            power_loss_percentage >= 0.5 and  # 50%+ power loss
            dead_delta >= 500000  # At least 500k dead troops
        )

        return high_power_loss or moderate_loss_high_deads

    @staticmethod
    def calculate_summary_stats(delta_stats: List[DeltaStats]) -> Dict:
        """Calculate summary statistics for a group of players."""
        if not delta_stats:
            return {
                "total_players": 0,
                "total_power_gain": 0,
                "total_power_loss": 0,
                "net_power_change": 0,
                "total_kp_gain": 0,
                "total_dead_troops": 0,
                "avg_power_gain": 0,
                "avg_kp_gain": 0,
                "avg_dead_troops": 0,
                "top_performers_count": 0,
                "needs_improvement_count": 0,
                "power_loss_players": 0,
                "zeroed_players": 0,
                "new_players": 0
            }

        # Calculate totals
        total_power_gain = sum(ds.power_delta for ds in delta_stats if ds.power_delta > 0)
        total_power_loss = sum(abs(ds.power_delta) for ds in delta_stats if ds.power_delta < 0)
        net_power_change = sum(ds.power_delta for ds in delta_stats)
        total_kp_gain = sum(ds.kill_points_delta for ds in delta_stats if ds.kill_points_delta > 0)
        total_dead_troops = sum(ds.dead_troops_delta for ds in delta_stats if ds.dead_troops_delta > 0)

        # Calculate averages only for players with positive gains
        player_count = len(delta_stats)
        players_with_power_gain = len([ds for ds in delta_stats if ds.power_delta > 0])
        players_with_kp_gain = len([ds for ds in delta_stats if ds.kill_points_delta > 0])
        players_with_dead_gain = len([ds for ds in delta_stats if ds.dead_troops_delta > 0])

        avg_power_gain = round(total_power_gain / players_with_power_gain, 0) if players_with_power_gain > 0 else 0
        avg_kp_gain = round(total_kp_gain / players_with_kp_gain, 0) if players_with_kp_gain > 0 else 0
        avg_dead_troops = round(total_dead_troops / players_with_dead_gain, 0) if players_with_dead_gain > 0 else 0

        # Count categories
        top_performers_count = sum(1 for ds in delta_stats if ds.is_top_performer)
        needs_improvement_count = sum(1 for ds in delta_stats if ds.needs_improvement)
        power_loss_players = sum(1 for ds in delta_stats if ds.power_delta < 0)
        zeroed_players = sum(1 for ds in delta_stats if ds.is_zeroed)
        new_players = sum(1 for ds in delta_stats if ds.is_new_player)

        return {
            "total_players": player_count,
            "total_power_gain": total_power_gain,
            "total_power_loss": total_power_loss,
            "net_power_change": net_power_change,
            "total_kp_gain": total_kp_gain,
            "total_dead_troops": total_dead_troops,
            "avg_power_gain": avg_power_gain,
            "avg_kp_gain": avg_kp_gain,
            "avg_dead_troops": avg_dead_troops,
            "top_performers_count": top_performers_count,
            "needs_improvement_count": needs_improvement_count,
            "power_loss_players": power_loss_players,
            "zeroed_players": zeroed_players,
            "new_players": new_players
        }

    @staticmethod
    def rank_players_by_metric(delta_stats: List[DeltaStats], metric: str, descending: bool = True) -> List[DeltaStats]:
        """Rank players by a specific metric."""
        valid_metrics = [
            'kill_points_delta', 'power_delta', 'dead_troops_delta',
            't45_delta', 'kp_per_dead', 'efficiency_score'
        ]

        if metric not in valid_metrics:
            raise ValueError(f"Invalid metric: {metric}. Valid metrics: {valid_metrics}")

        return sorted(
            delta_stats,
            key=lambda x: getattr(x, metric, 0),
            reverse=descending
        )

    @staticmethod
    def filter_players_by_criteria(delta_stats: List[DeltaStats], **criteria) -> List[DeltaStats]:
        """Filter players based on various criteria."""
        filtered = delta_stats

        if criteria.get('min_kp_delta'):
            filtered = [ds for ds in filtered if ds.kill_points_delta >= criteria['min_kp_delta']]

        if criteria.get('max_kp_delta'):
            filtered = [ds for ds in filtered if ds.kill_points_delta <= criteria['max_kp_delta']]

        if criteria.get('is_zeroed') is not None:
            filtered = [ds for ds in filtered if ds.is_zeroed == criteria['is_zeroed']]

        if criteria.get('is_top_performer') is not None:
            filtered = [ds for ds in filtered if ds.is_top_performer == criteria['is_top_performer']]

        if criteria.get('needs_improvement') is not None:
            filtered = [ds for ds in filtered if ds.needs_improvement == criteria['needs_improvement']]

        if criteria.get('alliance'):
            filtered = [ds for ds in filtered if ds.alliance == criteria['alliance']]

        if criteria.get('power_loss_only'):
            filtered = [ds for ds in filtered if ds.power_delta < 0]

        return filtered

# Global calculator instance
calculator = RoKCalculator()
