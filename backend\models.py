from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Boolean, BigInteger, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database import Base

class Player(Base):
    __tablename__ = "players"

    id = Column(Integer, primary_key=True, index=True)
    governor_id = Column(String(50), unique=True, index=True, nullable=False)  # Governor ID is required and unique
    name = Column(String(100), index=True, nullable=False)  # Player name is required
    alliance = Column(String(100), index=True, nullable=True)  # Alliance can be null
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), nullable=True)

    # Relationships
    stats = relationship("PlayerStat", back_populates="player")
    deltas = relationship("DeltaStat", back_populates="player")

class Scan(Base):
    __tablename__ = "scans"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String)  # e.g., "Pre-KVK", "Scan 1", etc.
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    is_baseline = Column(Boolean, default=False)  # Whether this is a baseline scan
    kvk_id = Column(Integer, ForeignKey("kvks.id"), nullable=True)  # Link to KvK
    kvk_phase = Column(String, nullable=True)  # Phase of KvK when scan was taken

    # Relationships
    player_stats = relationship("PlayerStat", back_populates="scan")
    kvk = relationship("KvK", back_populates="scans")

class PlayerStat(Base):
    __tablename__ = "player_stats"

    id = Column(Integer, primary_key=True, index=True)
    player_id = Column(Integer, ForeignKey("players.id", ondelete="CASCADE"), nullable=False)
    scan_id = Column(Integer, ForeignKey("scans.id", ondelete="CASCADE"), nullable=False)
    power = Column(BigInteger, nullable=False, default=0)  # Changed to BigInteger for large power values
    kill_points = Column(BigInteger, nullable=False, default=0)  # Total kill points from Excel
    kill_points_t1 = Column(BigInteger, nullable=False, default=0)  # T1 Kills from Excel
    kill_points_t2 = Column(BigInteger, nullable=False, default=0)  # T2 Kills from Excel
    kill_points_t3 = Column(BigInteger, nullable=False, default=0)  # T3 Kills from Excel
    kill_points_t4 = Column(BigInteger, nullable=False, default=0)  # T4 Kills from Excel
    kill_points_t5 = Column(BigInteger, nullable=False, default=0)  # T5 Kills from Excel
    total_kill_points = Column(BigInteger, nullable=False, default=0)  # Total Kills from Excel
    t45_kills = Column(BigInteger, nullable=False, default=0)  # T4-5 Kills from Excel
    ranged = Column(BigInteger, nullable=False, default=0)  # Ranged from Excel
    rss_gathered = Column(BigInteger, nullable=False, default=0)  # Rss Gathered from Excel
    rss_assistance = Column(BigInteger, nullable=False, default=0)  # Rss Assisted from Excel
    helps = Column(BigInteger, nullable=False, default=0)  # Helps from Excel
    dead_troops = Column(BigInteger, nullable=False, default=0)  # Deads from Excel
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)

    # Relationships
    player = relationship("Player", back_populates="stats")
    scan = relationship("Scan", back_populates="player_stats")

class DeltaStat(Base):
    __tablename__ = "delta_stats"

    id = Column(Integer, primary_key=True, index=True)
    player_id = Column(Integer, ForeignKey("players.id", ondelete="CASCADE"), nullable=False)
    start_scan_id = Column(Integer, ForeignKey("scans.id", ondelete="CASCADE"), nullable=False)
    end_scan_id = Column(Integer, ForeignKey("scans.id", ondelete="CASCADE"), nullable=False)
    power_delta = Column(BigInteger, nullable=False, default=0)  # Changed to BigInteger to match PlayerStat
    kill_points_delta = Column(BigInteger, nullable=False, default=0)  # Changed to BigInteger to match PlayerStat
    dead_troops_delta = Column(BigInteger, nullable=False, default=0)  # Changed to BigInteger to match PlayerStat
    kp_per_dead = Column(Float, default=0.0)  # Performance ratio
    is_zeroed = Column(Boolean, default=False, nullable=False)
    meets_kp_target = Column(Boolean, default=False, nullable=False)
    meets_dead_target = Column(Boolean, default=False, nullable=False)
    is_new_player = Column(Boolean, default=False, nullable=False)  # Player joined kingdom during KvK
    player_left_kingdom = Column(Boolean, default=False, nullable=False)  # Player left kingdom during KvK
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)

    # Relationships
    player = relationship("Player", back_populates="deltas")

class KvK(Base):
    __tablename__ = "kvks"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, index=True)
    start_date = Column(DateTime(timezone=True))
    end_date = Column(DateTime(timezone=True), nullable=True)
    status = Column(String, default="upcoming")  # upcoming, active, completed
    season = Column(Integer)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    scans = relationship("Scan", back_populates="kvk")

class Parameter(Base):
    __tablename__ = "parameters"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True)
    value = Column(Float)
    description = Column(String)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True)
    email = Column(String, unique=True, index=True, nullable=True)
    hashed_password = Column(String)
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

# Add performance indexes
Index('idx_player_governor_alliance', Player.governor_id, Player.alliance)
Index('idx_scan_kvk_timestamp', Scan.kvk_id, Scan.timestamp)
Index('idx_scan_baseline_kvk', Scan.is_baseline, Scan.kvk_id)
Index('idx_player_stat_scan_player', PlayerStat.scan_id, PlayerStat.player_id)
Index('idx_player_stat_power_kp', PlayerStat.power, PlayerStat.total_kill_points)
Index('idx_delta_stat_scans', DeltaStat.start_scan_id, DeltaStat.end_scan_id)
Index('idx_delta_stat_player_performance', DeltaStat.player_id, DeltaStat.kp_per_dead)
Index('idx_kvk_status_date', KvK.status, KvK.start_date)
Index('idx_user_active_admin', User.is_active, User.is_admin)